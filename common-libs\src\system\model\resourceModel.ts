export interface IResourceInfo {
    id?: number;
    name?: string;
    description?: string;
    url?: string;
    sort?: number;
    position?: string;
    type?: number;
    applicationCode?: string;
    applicationName?: string;
    priority?: number;
    parentId?: number;
    treePath?: string;
}

export interface IResourceInfoTreeRequest {
    groupId?: number;
    roleId?: number;
    parentId?: number;
    applicationId?: number;
    pathSnippets?: string;
    applicationCode?: string;
    types?: number[];
}

export interface IResourceInfoTreeResponse {
    id?: number;
    name?: string;
    keepAlive?: number;
    description?: string;
    url?: string;
    sort?: number;
    position?: string;
    type?: number;
    applicationCode?: string;
    applicationName?: string;
    priority?: number;
    parentId?: number;
    treePath?: string;
    children?: IResourceInfoTreeResponse[]
}

export interface IOwnApplicationCodesResponse {
    code?: string;
    name?: string;
    url?: string;
}

export interface IResourceTreeNode extends IResourceInfoTreeResponse{
    key?: number;
}

export interface IResourceSaveRequest {
    id?: number;
    name?: string;
    keepAlive?: number;
    description?: string;
    url?: string;
    sort?: number;
    position?: string;
    type?: number;
    applicationCode?: string;
    applicationName?: string;
    priority?: number;
    parentId?: number;
    treePath?: string;
}

export interface IResourceDeleteRequest {
    ids?: (number|undefined)[];
}