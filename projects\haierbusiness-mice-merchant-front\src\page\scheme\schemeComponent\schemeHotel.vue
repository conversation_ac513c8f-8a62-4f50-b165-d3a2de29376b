<script setup lang="ts">
// 方案互动-酒店需求方案
import { message } from 'ant-design-vue';
import { onMounted, ref, reactive, watch, computed, nextTick, defineProps, defineEmits } from 'vue';

import { errorModal, resolveParam, routerParam } from '@haierbusiness-front/utils';
import { hotelLevelAllConstant, HotelsArr } from '@haierbusiness-front/common-libs';
import { useRoute } from 'vue-router';
import { schemeApi } from '@haierbusiness-front/apis';

import { debounce } from 'lodash';

const route = useRoute();

const props = defineProps({
  hotels: {
    type: Array,
    default: [],
  },
  schemeHotels: {
    type: Array,
    default: [],
  },
  schemeType: {
    // 方案提报类型 // 查看需求-view / 未提报-notReported / 已提报-reported / 查看方案-schemeView / 待竞价 - notBidding / 竞价完成 - biddingView / 账单上传 - billUpload
    type: String,
    default: '',
  },
  showBindingScheme: {
    type: Boolean,
    default: true,
  },
  merchantType: {
    type: Number,
    default: null,
  },
});

const emit = defineEmits(['hotelsEmit']);

const miceId = ref<number>('');

const newSchemeList = ref<array>([]);

const addHotelLoading = ref<boolean>(false);
const hotelLoading = ref<boolean>(false);

const hotelOpen = ref<boolean>(false);
const hotelValue = ref(null);
const hotelList = ref<array>([]);
const hotelAddObj = ref<HotelsArr>({});

// TODO
const specialPermission = ref<boolean>(false); // 特殊权限

watch(
  () => [props.hotels, props.schemeHotels],
  () => {
    if (props.schemeHotels.length > 0) {
      newSchemeList.value = props.schemeHotels.map((e, idx) => {
        return {
          ...e,
          tempId: e.tempId
            ? e.tempId
            : props.merchantType === 1 || props.merchantType === 2
            ? e.id || 100000000000 + e.miceDemandPushHotelId
            : Date.now() + idx, // 直签酒店
        };
      });

      emit('hotelsEmit', [...newSchemeList.value]);
    }
  },
  {
    immediate: true,
    deep: true,
  },
);

// 酒店需求方案
const hotelColumns1 = [
  {
    name: 'schemeIndex',
    dataIndex: '1',
    width: 72,
    ellipsis: true,
    align: 'center',
    className: 'interact_table_bgc_gray',
    customRender: ({ index }) => {
      return '酒店' + (index + 1);
    },
  },
  {
    title: 'schemeTitle',
    dataIndex: '2',
    // width: 280,
    ellipsis: true,
    customRender: ({ record }) => {
      return record.centerMarker + '附近';
    },
  },
  {
    title: 'schemeValue',
    dataIndex: '3',
    // width: 240,
    ellipsis: true,
    customRender: ({ record }) => {
      return hotelLevelAllConstant.ofType(record.level)?.desc || '-';
    },
  },
];
const hotelColumns2 = [
  {
    name: 'schemeIndex',
    dataIndex: '1',
    width: 72,
    ellipsis: true,
    align: 'center',
    className: 'interact_table_bgc_gray',
    customRender: ({ index }) => {
      return '酒店' + (index + 1);
    },
  },
  {
    title: 'schemeTitle',
    dataIndex: '2',
    // width: 280,
    ellipsis: true,
    customRender: ({ record }) => {
      return record.hotelName;
    },
  },
  {
    title: 'schemeValue',
    dataIndex: '3',
    // width: 240,
    ellipsis: true,
    customRender: ({ record }) => {
      return hotelLevelAllConstant.ofType(record.level)?.desc || '-';
    },
  },
];

const addHotel = (idx: number) => {
  hotelValue.value = null;
  hotelOpen.value = true;

  getHotelList();
};
const delHotel = (idx: number) => {
  newSchemeList.value.splice(idx, 1);

  emit('hotelsEmit', [...newSchemeList.value]);
};

const fetchHotel = debounce((value) => {
  hotelValue.value = value;

  getHotelList();
}, 300);
const handleChange = (hotelObj: HotelsArr) => {
  hotelAddObj.value = hotelObj;
};

const handleOk = () => {
  if (!hotelValue.value) {
    message.error('请选择酒店');
    return;
  }

  hotelOpen.value = false;

  newSchemeList.value.push({
    ...hotelAddObj.value,

    tempId: Date.now(),
    hotelAddress: hotelAddObj.value.hotelAddress,
  });

  emit('hotelsEmit', [...newSchemeList.value]);
};

// 查询符合条件酒店
const getHotelList = async () => {
  if (!miceId.value) {
    message.error('查询失败！');
    return;
  }

  addHotelLoading.value = true;

  const res = await schemeApi.pushHotelsDetails({
    hotelName: hotelValue.value,
    miceId: miceId.value,
    // pageNum: 1,
    // pageSize: 20,
  });

  addHotelLoading.value = false;

  hotelList.value = res || [];

  // 不可选
  hotelList.value.forEach((e) => {
    e.disabled = false;

    newSchemeList.value.forEach((e1) => {
      if (e.id == e1.miceDemandPushHotelId) {
        e.disabled = true;
      }
    });
  });
};

// 查询抢单锁定酒店详情
const getHotelDetail = async (id) => {
  if (!id) {
    return;
  }

  hotelLoading.value = true;

  const res = await schemeApi.lockHotelDetails({
    hotelLockId: id,
  });

  newSchemeList.value = [];

  if (res && res.length > 0) {
    newSchemeList.value = res.map((e, idx) => {
      return {
        tempId: Date.now() + idx, // 直签酒店
        miceDemandHotelId: props.hotels[idx]?.id,
        miceDemandPushHotelId: e.miceDemandPushHotelId,
        hotelName: e.hotelName,
        hotelCode: e.hotelCode,
        decorationYear: e.decorationYear,
        cityId: e.cityId,
        cityName: e.cityName,
        districtId: e.districtId,
        districtName: e.districtName,
        hotelAddress: e.hotelAddress,
        level: e.level,
        latitude: e.latitude,
        longitude: e.longitude,
        distance: e.distance,
      };
    });
  }

  emit('hotelsEmit', [...newSchemeList.value]);

  hotelLoading.value = false;
};

onMounted(async () => {
  const record = resolveParam(route.query.record as string);
  miceId.value = record.miceId;

  if (record.hotelLockId && (props.merchantType !== 1 || props.schemeHotels.length === 0)) {
    // 非直签酒店
    // 方案酒店为空
    getHotelDetail(record.hotelLockId);
  }
});
</script>

<template>
  <!-- 酒店需求方案 -->
  <div class="scheme_hotel">
    <div class="interact_title">
      <div class="interact_shu mr20"></div>
      <span>酒店需求方案</span>
    </div>

    <div class="common_table mt16">
      <div class="common_table_l" v-show="showBindingScheme">
        <a-table
          v-if="props.schemeType !== 'notBidding' && props.schemeType !== 'biddingView'"
          :columns="hotelColumns1"
          :data-source="props.hotels"
          bordered
          :show-header="false"
          size="small"
          :pagination="false"
        >
        </a-table>

        <!-- 竞价 - 标的方案 -->
        <a-table
          v-else
          :loading="hotelLoading"
          :columns="hotelColumns2"
          :data-source="newSchemeList"
          bordered
          :show-header="false"
          size="small"
          :pagination="false"
        >
        </a-table>
      </div>
      <div class="common_table_divide"></div>
      <div class="common_table_r flex">
        <a-table
          :loading="hotelLoading"
          :columns="hotelColumns2"
          :data-source="newSchemeList"
          bordered
          :show-header="false"
          size="small"
          :pagination="false"
        >
        </a-table>

        <!-- 特殊权限 -->
        <div v-if="specialPermission" class="action_icons">
          <div v-for="(item, index) in newSchemeList" :key="index" class="flex">
            <a-popconfirm
              v-if="newSchemeList.length > 1"
              :title="'确认删除酒店' + (index + 1) + '？'"
              ok-text="确认"
              cancel-text="取消"
              @confirm="delHotel(index)"
            >
              <div class="del_icon ml12"></div>
            </a-popconfirm>

            <div v-if="newSchemeList.length - index === 1" class="add_icon ml8" @click="addHotel(index)"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 酒店新增 -->
    <a-modal v-model:open="hotelOpen" title="请选择酒店：" @ok="handleOk">
      <a-select
        style="width: 100%"
        v-model:value="hotelValue"
        placeholder="请选择酒店"
        show-search
        @search="fetchHotel"
      >
        <a-select-option
          v-for="item in hotelList"
          :disabled="item.disabled"
          :key="item.id"
          :value="item.id"
          @click="handleChange(item)"
        >
          {{ item.hotelName }}
        </a-select-option>
      </a-select>
    </a-modal>
  </div>
</template>

<style scoped lang="less">
.scheme_hotel {
  .action_icons {
    .add_icon,
    .del_icon {
      width: 16px;
      height: 40px;

      cursor: pointer;
      background: url('@/assets/image/common/add_gray.png');
      background-repeat: no-repeat;
      background-size: 16px 16px;
      background-position: center;
    }
    .del_icon {
      background: url('@/assets/image/common/del_red.png');
      background-repeat: no-repeat;
      background-size: 16px 16px;
      background-position: center;
    }
  }
}
</style>
