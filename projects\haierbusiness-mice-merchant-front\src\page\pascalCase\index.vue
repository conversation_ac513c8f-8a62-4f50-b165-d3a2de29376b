<script setup lang="ts">
import { ref } from 'vue';
import GiftList from './component/giftList.vue'; //礼品
import InsuranceList from './component/insuranceList.vue'; //保险
import HotelList from './component/hotelList.vue'; //酒店
import { serviceProviderApi } from '@haierbusiness-front/apis';
import { onMounted } from 'vue';
import { MerchantTypeEnum } from '@haierbusiness-front/common-libs';
//通知弹框逻辑
import notice from '../component/Modal.vue';
const detailData = ref<any>({});

//通知弹框逻辑
const noticeVisible = ref(false)
//
const handleShow = () => {
  noticeVisible.value = false
  console.log(noticeVisible.value);
}
const handlecancel = () => {
  noticeVisible.value = false
}

onMounted(async () => {
  const res = await serviceProviderApi.get(undefined);
  detailData.value = res;

  //通知弹框
  const showValue = sessionStorage.getItem('Show')
  if (showValue === null) {
    sessionStorage.setItem('Show', JSON.stringify(false))
    noticeVisible.value = true
  } else {
    noticeVisible.value = JSON.parse(showValue)
  }
});
</script>

<template>
  <div style="background-color: #ffff; width: 100%; padding: 10px;">
    <GiftList v-if="detailData.merchantType === MerchantTypeEnum.GIFT" />
    <InsuranceList v-if="detailData.merchantType === MerchantTypeEnum.INSURANCE" />
    <HotelList v-if="detailData.merchantType === MerchantTypeEnum.HOTEL" />
  </div>
  <!-- 通知弹框逻辑 -->
  <div v-if="noticeVisible">
      <notice @ok="handleShow" @cancel="handlecancel"></notice>
    </div>
</template>

<style scoped lang="less"></style>
