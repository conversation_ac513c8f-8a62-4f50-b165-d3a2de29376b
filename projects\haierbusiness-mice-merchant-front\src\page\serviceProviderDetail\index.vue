<!-- 服务商信息详情 -->
<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Table as hTable,
  Tag as hTag,
  message,
  Modal,
} from 'ant-design-vue';
import { h } from 'vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import {
  FileTypeConstant,
  getFileType,
  getFreezeTypeInfo,
  ServiceProviderFreezeStatusMap,
  ServiceProviderFreezeStatusEnum,
  YesNoEnum,
  getYesNoLabel,
  ApprovalStatusMap,
  ApprovalStatusEnum,
} from '@haierbusiness-front/common-libs';
import dayjs, { Dayjs } from 'dayjs';
import { miceBidManServiceProviderApi, serviceProviderApi } from '@haierbusiness-front/apis';
import {
  ServiceProviderType,
  ServiceProviderState,
  ServiceProviderStateEnum,
  ServiceProviderStateMap,
  ExamineStateEnum,
  ExamineStateMap,
  ViolationTypeEnum,
  ViolationTypeMap,
  TrialStateEnum,
  TrialStateMap,
  MerchantTypeEnum,
  MerchantTypeMap,
  BondDetail,
  MarginStatusEnum,
  MarginStatusMap,
} from '@haierbusiness-front/common-libs';
import { computed, ref, onMounted } from 'vue';
//通知弹框逻辑
import notice from '../component/Modal.vue';
import { usePagination } from 'vue-request';
import router from '../../router';

const currentRouter = ref();
const detailData = ref<any>({});
// 获取详情数据
const getServiceProviderDetail = async () => {
  try {
    const res = await serviceProviderApi.get();
    console.log('res', res);
    detailData.value = res;
    if (res) {
      // TODO: 根据接口返回数据结构更新页面数据
      console.log('服务商详情数据:', res);
    }
  } catch (error) {
    console.error('获取服务商详情失败:', error);
  }
};
//通知弹框逻辑
const noticeVisible = ref(false)
//
const handleShow = () => {
  noticeVisible.value = false
  console.log(noticeVisible.value);
}
const handlecancel = () => {
  noticeVisible.value = false
}

onMounted(async () => {
  currentRouter.value = await router;
  getSummaryData();
  // 调用获取详情接口
  await getServiceProviderDetail();
  //通知弹框
  const showValue = sessionStorage.getItem('Show')
  if (showValue === null) {
    sessionStorage.setItem('Show', JSON.stringify(false))
    noticeVisible.value = true
  } else {
    noticeVisible.value = JSON.parse(showValue)
  }
});

// 获取保证金汇总数据
const getSummaryData = async () => {
  try {
    const res = await miceBidManServiceProviderApi.getMerchantSummary({
      // merchantId: id
    });
    if (res) {
      depositBalanceValue.value = res;
    }
  } catch (error) {
    console.error('获取保证金汇总数据失败:', error);
  }
};
// 获取状态文本
const getStateText = (state: number | null | undefined) => {
  return ServiceProviderStateMap[state as ServiceProviderStateEnum] || '';
};
// 获取试用状态文本
const getTrialStateText = (state: number | string | null | undefined) => {
  if (state === undefined || state === null) return '-';
  return typeof state === 'string'
    ? state
    : state === TrialStateEnum.FORMAL
      ? TrialStateMap[TrialStateEnum.FORMAL]
      : state === TrialStateEnum.TRIAL
        ? TrialStateMap[TrialStateEnum.TRIAL]
        : '-';
};

// 获取是否转正状态文本
const getIsRegularizedText = (state: number | string | null | undefined) => {
  if (state === undefined || state === null) return '-';
  // 如果是正式状态，返回"是"（已转正）
  if (state === TrialStateEnum.FORMAL) {
    return getYesNoLabel(YesNoEnum.YES);
  }
  // 如果是试用状态，返回"否"（未转正）
  if (state === TrialStateEnum.TRIAL) {
    return getYesNoLabel(YesNoEnum.NO);
  }
  return '-';
};

// 获取商户类型文本
const getMerchantTypeText = (type: number | null | undefined) => {
  return type ? MerchantTypeMap[type as MerchantTypeEnum] || '-' : '-';
};

// 企业基本信息
const baseInfo = ref({
  name: '青岛城市建设集团海景花园酒店管理有限公司希尔顿酒店',
  type: ServiceProviderType.HOTEL,
  merchantCode: 'V9050263',
  socialCreditCode: 'YU900000000111111',
  overseasCode: 'Z6006060',
  clientCode: '2000012',
  gmtCreate: '2023.12.1',
  state: ServiceProviderState.NORMAL,
  trialState: TrialStateMap[TrialStateEnum.FORMAL],
});

// 合同信息表格列配置
const contractColumns: ColumnType[] = [
  {
    title: '合同编号',
    dataIndex: 'contractCode',
    width: '180px',
  },
  {
    title: '合同有效期',
    dataIndex: 'signDate',
    width: '200px',
  },
  {
    title: '状态',
    dataIndex: 'state',
    width: '100px',
    customRender: ({ text }) => ServiceProviderStateMap[text as ServiceProviderStateEnum] || '',
  },
  {
    title: '签订日期',
    dataIndex: 'signDate',
    width: '120px',
  },
  {
    title: '合同附件',
    dataIndex: 'contractUrl',
    width: '100px',
    customRender: ({ record }) => {
      return h(
        hButton,
        {
          type: 'link',
          size: 'small',
          onClick: () => {
            if (record.contractUrl) {
              window.open(record.contractUrl, '_blank');
            } else {
              message.warning('暂无合同附件');
            }
          },
        },
        () => '查看',
      );
    },
  },
];

// 银行信息表格列配置
const bankColumns: ColumnType[] = [
  {
    title: '企业名称',
    dataIndex: 'accountHolderName',
    width: '200px',
  },

  {
    title: '银行账号',
    dataIndex: 'accountNumber',
    width: '200px',
  },

  {
    title: '开户行',
    dataIndex: 'bankBranchAddress',
    width: '200px',
  },
  {
    title: '所在国家',
    dataIndex: 'bankCountry',
    width: '200px',
  },
];

// 状态变更表格列配置
const statusColumns: ColumnType[] = [
  {
    title: '冻结类型',
    dataIndex: 'freezeType',
    width: '120px',
    ellipsis: true,
    customRender: ({ text }: { text: number }) => {
      if (text === undefined || text === null) return '-';
      const typeInfo = getFreezeTypeInfo(text);
      return typeInfo ? typeInfo.name : '-';
    },
  },
  {
    title: '理由',
    dataIndex: 'freezeReason',
    width: '150px',
  },
  {
    title: '见证性材料',
    dataIndex: 'attachmentFiles',
    width: '120px',
    customRender: ({ text }: { text: any[] }) => {
      if (!text || !Array.isArray(text) || text.length === 0) return '-';
      return h(
        'div',
        {
          class: 'attachment-list',
        },
        text.map((attachment, index) =>
          h(
            hButton,
            {
              type: 'link',
              size: 'small',
              key: index,
              onClick: () => {
                if (attachment && attachment.path) {
                  window.open(attachment.path, '_blank');
                }
              },
            },
            () => `附件${index + 1}`,
          ),
        ),
      );
    },
  },
  {
    title: '创建人',
    dataIndex: 'createName',
    width: '120px',
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    width: '120px',
  },
  {
    title: '更新时间',
    dataIndex: 'gmtModified',
    width: '100px',
    ellipsis: true,
  },
  {
    title: '状态',
    dataIndex: 'state',
    width: '100px',
    customRender: ({ text }: { text: number }) => {
      if (text === undefined || text === null) return '-';
      return ServiceProviderFreezeStatusMap[text as ServiceProviderFreezeStatusEnum] || '-';
    },
  },
  {
    title: '审批意见',
    dataIndex: 'remark',
    width: '150px',
  },
];

// 违规记录表格列配置
const violationColumns: ColumnType[] = [
  {
    title: '违规单号',
    dataIndex: 'examineCode',
    width: '150px',
  },
  {
    title: '违规类型',
    dataIndex: 'type',
    width: '120px',
    customRender: ({ text }: { text: number }) => (text ? ViolationTypeMap[text as ViolationTypeEnum] || '-' : '-'),
  },
  {
    title: '考核条目',
    dataIndex: 'entry',
    width: '150px',
  },
  {
    title: '分值',
    dataIndex: 'score',
    width: '100px',
  },
  {
    title: '罚款',
    dataIndex: 'fine',
    width: '100px',
  },
  {
    title: '创建时间',
    dataIndex: 'violationTime',
    width: '150px',
  },
  {
    title: '状态',
    dataIndex: 'examineState',
    width: '100px',
    customRender: ({ text }: { text: number }) => (text ? ExamineStateMap[text as ExamineStateEnum] || '-' : '-'),
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: '100px',
    customRender: ({ record }) => {
      return h(
        hButton,
        {
          type: 'link',
          size: 'small',
          onClick: () => handleViolationView(record),
        },
        () => '查看',
      );
    },
  },
];

// 产品信息表格列配置
const productColumns: ColumnType[] = [
  {
    title: '酒店名称',
    dataIndex: 'platformHotelName',
    width: '200px',
  },
  {
    title: '酒店地址',
    dataIndex: 'platformHotelAddress',
    width: '250px',
  },

  {
    title: '酒店编码',
    dataIndex: 'platformHotelCode',
    width: '100px',
  },

  {
    title: '操作',
    dataIndex: 'operation',
    width: '100px',
    customRender: () => {
      return h(
        hButton,
        {
          type: 'link',
          onClick: () => {
            message.info('功能正在开发中');
          },
        },
        () => '查看',
      );
    },
  },
];

// 简化为单一的余额值
const depositBalanceValue = ref();

// 保证金记录表格列配置
const depositColumns: ColumnType[] = [
  {
    title: '单号',
    dataIndex: 'recordNo',
    width: '150px',
  },
  {
    title: '操作类别',
    dataIndex: 'type',
    width: '120px',
    customRender: ({ text }) => {
      const type = text === 1 ? '缴纳' : text === 2 ? '退款' : '-';
      const color = text === 1 ? 'green' : text === 2 ? 'blue' : 'default';
      return h(hTag, { color }, () => `保证金${type}`);
    },
  },
  {
    title: '金额',
    dataIndex: 'amount',
    width: '120px',
  },
  {
    title: '审批状态',
    dataIndex: 'state',
    width: '120px',
    customRender: ({ text }) => {
      return MarginStatusMap[text as MarginStatusEnum] || '-';
    },
  },
  {
    title: 'SAP收款单号',
    dataIndex: 'sapReceiveNo',
    width: '150px',
  },
  {
    title: '确认收款人',
    dataIndex: 'receiveName',
    width: '120px',
  },
  {
    title: '确认时间',
    dataIndex: 'receiveTime',
    width: '150px',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: '100px',
    customRender: ({ record }) => {
      return h(
        hButton,
        {
          type: 'link',
          size: 'small',
          onClick: () => handleView(record),
        },
        () => '查看',
      );
    },
  },
];

// 保证金记录数据
const depositData = ref([
  {
    orderNo: 'BZJ20240301001',
    operationType: '缴纳',
    amount: '50000',
    approvalStatus: '已审批',
    sapReceiptNo: 'SAP2024030100123',
    confirmer: '王明',
    confirmTime: '2024-03-01 14:30:22',
  },
  {
    orderNo: 'BZJ20240410002',
    operationType: '退还',
    amount: '20000',
    approvalStatus: '审批中',
    sapReceiptNo: '',
    confirmer: '',
    confirmTime: '',
  },
]);

// 转正记录表格列配置
const regularizationColumns: ColumnType[] = [
  {
    title: '操作类型',
    dataIndex: 'operateType',
    width: '100px',
    customRender: ({ text }: { text: string }) => getTrialStateText(Number(text)),
    ellipsis: true,
  },
  {
    title: '理由',
    dataIndex: 'reason',
    width: '150px',
  },
  {
    title: '转正需承接会议',
    dataIndex: 'trialEndMiceNum',
    width: '100px',
    ellipsis: true,
  },
  {
    title: '见证性材料',
    dataIndex: 'attachmentFiles',
    width: '100px',
    customRender: ({ text }: { text: any[] }) => {
      if (!text || !Array.isArray(text) || text.length === 0) return '-';
      return h(
        'div',
        {
          class: 'attachment-list',
        },
        text.map((attachment, index) =>
          h(
            hButton,
            {
              type: 'link',
              size: 'small',
              key: index,
              onClick: () => {
                if (attachment && attachment.path) {
                  window.open(attachment.path, '_blank');
                }
              },
            },
            () => `附件${index + 1}`,
          ),
        ),
      );
    },
    ellipsis: true,
  },
  {
    title: '创建人',
    dataIndex: 'createName',
    width: '100px',
    ellipsis: true,
  },
  {
    title: '创建时间',
    dataIndex: 'gmtCreate',
    width: '100px',
    ellipsis: true,
  },
  {
    title: '状态',
    dataIndex: 'approveState',
    width: '100px',
    customRender: ({ text }: { text: number }) => {
      if (text === undefined || text === null) return '-';
      return ApprovalStatusMap[text as ApprovalStatusEnum] || '-';
    },
  },
  {
    title: '审批意见',
    dataIndex: 'remark',
    width: '150px',
    ellipsis: true,
  },
];

//弹窗处理
const detailModalVisible = ref(false);
const currentDetail = ref<BondDetail>({
  recordNo: '',
  amount: undefined,
  gmtCreate: '',
  sapReceiveNo: '',
  receiveName: '',
  receiveTime: '',
});

const violationDetail = ref<BondDetail>({
  recordNo: '',
  amount: undefined,
  gmtCreate: '',
  sapReceiveNo: '',
  receiveName: '',
  receiveTime: '',
});
const title = ref('');

// 查看保证金记录详情
const handleView = (res: any) => {
  console.log('查看详情:', res);
  title.value = '保证金详情';
  detailModalVisible.value = true;
  currentDetail.value = res;
};

// 查看违规记录详情
const handleViolationView = (res: any) => {
  console.log('查看违规记录详情:', res);
  // 跳转到详情页，传递ID和编辑模式参数
  currentRouter.value.push({
    path: '/mice-merchant/serviceProviderDetail/violation',
    query: {
      id: res.id,
    },
  });
};

// 开票信息表格列配置
// const invoiceColumns: ColumnType[] = [
//   {
//     title: '企业名称',
//     dataIndex: 'companyName',
//     width: '200px',
//   },
//   {
//     title: '企业税号',
//     dataIndex: 'taxNumber',
//     width: '180px',
//   },
//   {
//     title: '银行账号',
//     dataIndex: 'bankAccount',
//     width: '180px',
//   },
//   {
//     title: '企业电话',
//     dataIndex: 'phone',
//     width: '150px',
//   },
//   {
//     title: '开户行',
//     dataIndex: 'bank',
//     width: '200px',
//   },
//   {
//     title: '是否默认',
//     dataIndex: 'isDefault',
//     width: '100px',
//   },
//   {
//     title: '操作',
//     dataIndex: 'operation',
//     width: '120px',
//     customRender: ({ record }) => {
//       return h(hButton, {
//         type: 'link',
//         onClick: () => handleInvoiceDefaultChange(record),
//       }, () => record.isDefault === '是' ? '取消默认' : '设为默认')
//     }
//   }
// ];

// 开票信息数据
// const invoiceData = ref([
//   {
//     companyName: '青岛海景花园酒店管理有限公司',
//     taxNumber: '91370213XXXXXXXX4Y',
//     bankAccount: '*****************',
//     phone: '0532-********',
//     bank: '中国建设银行股份有限公司青岛金沙滩支行',
//     isDefault: '是',
//   },
//   {
//     companyName: '青岛希尔顿酒店有限公司',
//     taxNumber: '91370213XXXXXXXX5Z',
//     bankAccount: '*****************',
//     phone: '0532-********',
//     bank: '中国工商银行股份有限公司青岛市南支行',
//     isDefault: '否',
//   }
// ]);

// 处理开票信息默认状态切换
// const handleInvoiceDefaultChange = (record: any) => {
//   // 如果当前记录已经是默认，则取消默认
//   if (record.isDefault === '是') {
//     record.isDefault = '否';
//   } else {
//     // 如果要设为默认，先将其他所有记录设为非默认
//     invoiceData.value.forEach(item => {
//       item.isDefault = '否';
//     });
//     // 然后将当前记录设为默认
//     record.isDefault = '是';
//   }
// };

// 收款账户信息表格列配置
// const receivingAccountColumns: ColumnType[] = [
//   {
//     title: '企业名称',
//     dataIndex: 'companyName',
//     width: '200px',
//   },
//   {
//     title: '企业税号',
//     dataIndex: 'taxNumber',
//     width: '180px',
//   },
//   {
//     title: '银行账号',
//     dataIndex: 'bankAccount',
//     width: '180px',
//   },
//   {
//     title: '企业电话',
//     dataIndex: 'phone',
//     width: '150px',
//   },
//   {
//     title: '开户行',
//     dataIndex: 'bank',
//     width: '200px',
//   },
//   {
//     title: '是否默认',
//     dataIndex: 'isDefault',
//     width: '100px',
//   },
//   {
//     title: '操作',
//     dataIndex: 'operation',
//     width: '120px',
//     customRender: ({ record }) => {
//       return h(hButton, {
//         type: 'link',
//         onClick: () => handleReceivingAccountDefaultChange(record),
//       }, () => record.isDefault === '是' ? '取消默认' : '设为默认')
//     }
//   }
// ];

// 收款账户信息数据
// const receivingAccountData = ref([
//   {
//     companyName: '青岛海景花园酒店管理有限公司',
//     taxNumber: '91370213XXXXXXXX4Y',
//     bankAccount: '*****************',
//     phone: '0532-********',
//     bank: '中国建设银行股份有限公司青岛金沙滩支行',
//     isDefault: '是',
//   },
//   {
//     companyName: '青岛市南区海景酒店管理中心',
//     taxNumber: '91370213XXXXXXXX5Z',
//     bankAccount: '*****************',
//     phone: '0532-********',
//     bank: '中国工商银行股份有限公司青岛市南支行',
//     isDefault: '否',
//   }
// ]);

// 处理收款账户信息默认状态切换
// const handleReceivingAccountDefaultChange = (record: any) => {
//   // 如果当前记录已经是默认，则取消默认
//   if (record.isDefault === '是') {
//     record.isDefault = '否';
//   } else {
//     // 如果要设为默认，先将其他所有记录设为非默认
//     receivingAccountData.value.forEach(item => {
//       item.isDefault = '否';
//     });
//     // 然后将当前记录设为默认
//     record.isDefault = '是';
//   }
// };

// 冻结记录表格列配置
// const freezeRecordColumns: ColumnType[] = [
//   {
//     title: '操作类型',
//     dataIndex: 'operationType',
//     width: '120px',
//   },
//   {
//     title: '理由',
//     dataIndex: 'reason',
//     width: '200px',
//   },
//   {
//     title: '见证性材料',
//     dataIndex: 'material',
//     width: '120px',
//     customRender: ({ text }) => {
//       return h(hButton, {
//         type: 'link',
//         onClick: () => {
//           window.open(text, '_blank'); // 假设material字段存储的是文件URL
//           console.log('查看冻结记录见证性材料');
//         }
//       }, () => '查看')
//     }
//   },
//   {
//     title: '创建人',
//     dataIndex: 'creator',
//     width: '120px',
//   },
//   {
//     title: '创建时间',
//     dataIndex: 'createTime',
//     width: '150px',
//   },
//   {
//     title: '审批状态',
//     dataIndex: 'approvalStatus',
//     width: '100px',
//   },
//   {
//     title: '审批意见',
//     dataIndex: 'approvalOpinion',
//     width: '200px',
//   }
// ];

// 冻结记录数据
// const freezeRecordData = ref([
//   {
//     operationType: '账户冻结',
//     reason: '涉嫌违规操作，暂时冻结账户',
//     material: '违规证据.pdf',
//     creator: '王刚/22068888',
//     createTime: '2024-04-10 15:30:22',
//     approvalStatus: '已审批',
//     approvalOpinion: '核实情况属实，同意冻结'
//   },
//   {
//     operationType: '解除冻结',
//     reason: '违规问题已解决，申请解除冻结',
//     material: '整改报告.pdf',
//     creator: '王刚/22068888',
//     createTime: '2024-04-20 09:45:16',
//     approvalStatus: '审批中',
//     approvalOpinion: ''
//   }
// ]);
</script>

<template>
  <div class="service-provider-detail-container">
    <div class="service-provider-detail-content">
      <!-- 基本信息 -->
      <div class="info-card">
        <div class="card-title main-title">{{ detailData.merchantName }}</div>
        <div class="info-content">
          <h-row :gutter="24">
            <h-col :span="24">
              <h-tag class="company-type">{{ getMerchantTypeText(detailData.merchantType) }}</h-tag>
            </h-col>
            <h-col :span="5">
              <div class="info-item">
                <span class="label">企业编码:</span>
                <span class="value">{{ detailData.merchantCode }}</span>
              </div>
            </h-col>
            <h-col :span="7">
              <div class="info-item">
                <span class="label">统一社会信用代码:</span>
                <span class="value">{{ detailData.unifiedSocialCreditCode }}</span>
              </div>
            </h-col>
            <h-col :span="6">
              <div class="info-item">
                <span class="label">海外Z码:</span>
                <span class="value">{{ detailData.merchantAbroadCode }}</span>
              </div>
            </h-col>
            <h-col :span="6">
              <div class="info-item">
                <span class="label">客户码:</span>
                <span class="value">{{ detailData.merchantCode }}</span>
              </div>
            </h-col>
            <h-col :span="5">
              <div class="info-item">
                <span class="label">引入时间:</span>
                <span class="value">{{ detailData.gmtCreate }}</span>
              </div>
            </h-col>
            <h-col :span="7">
              <div class="info-item">
                <span class="label">状态:</span>
                <span class="value">{{ getStateText(detailData.state) }}</span>
              </div>
            </h-col>
            <h-col :span="6">
              <div class="info-item">
                <span class="label">是否转正:</span>
                <span class="value">{{ getIsRegularizedText(detailData.trialState) }}</span>
              </div>
            </h-col>
          </h-row>
        </div>
      </div>

      <!-- 合同信息 - 所有类型都显示 -->
      <div class="info-card">
        <div class="card-title">合同信息</div>
        <div class="card-content">
          <h-table :columns="contractColumns" :data-source="detailData.merchantContractRecords || []"
            :pagination="false" size="small" :bordered="true" />
        </div>
      </div>

      <!-- 供应商银行信息 - 酒店和旅行社显示 -->
      <div class="info-card"
        v-if="baseInfo.type === ServiceProviderType.HOTEL || baseInfo.type === ServiceProviderType.TRAVEL_AGENCY">
        <div class="card-title">供应商银行信息</div>
        <div class="card-content">
          <h-table :columns="bankColumns" :data-source="detailData.merchantBankRecords || []" :pagination="false"
            size="small" :bordered="true" />
        </div>
      </div>

      <!-- 开票信息 - 众淼显示 -->
      <!-- <div class="info-card" v-if="baseInfo.type === ServiceProviderType.ZHONGMIAO">
        <div class="card-title">开票信息</div>
        <div class="card-content">
          <h-table :columns="invoiceColumns" :data-source="invoiceData" :pagination="false" size="small"
            :bordered="true" />
        </div>
      </div> -->

      <!-- 收款账户信息 - 众淼显示 -->
      <!-- <div class="info-card" v-if="baseInfo.type === ServiceProviderType.ZHONGMIAO">
        <div class="card-title">收款账户信息</div>
        <div class="card-content">
          <h-table :columns="receivingAccountColumns" :data-source="receivingAccountData" :pagination="false"
            size="small" :bordered="true" />
        </div>
      </div> -->

      <!-- 保证金记录 - 旅行社显示 -->
      <div class="info-card" v-if="detailData.merchantType === MerchantTypeEnum.TRAVEL">
        <div class="card-title deposit-header">
          <span>保证金记录</span>
          <div class="deposit-balance-simple">
            <span class="balance-label">保证金余额:</span>
            <span class="balance-value">¥{{ depositBalanceValue.balance }}</span>
          </div>
        </div>
        <div class="card-content">
          <h-table :columns="depositColumns" :data-source="detailData.earnestRecords || []" :pagination="false"
            size="small" :bordered="true" />
        </div>
      </div>

      <!-- 状态变更记录 - 酒店和旅行社显示 -->
      <div class="info-card"
        v-if="baseInfo.type === ServiceProviderType.HOTEL || baseInfo.type === ServiceProviderType.TRAVEL_AGENCY">
        <div class="card-title">异常原因记录</div>
        <div class="card-content">
          <h-table :columns="statusColumns" :data-source="detailData.merchantFreezeRecords || []" :pagination="false"
            size="small" :bordered="true" />
        </div>
      </div>

      <!-- 冻结记录 - 众淼显示 -->
      <!-- <div class="info-card" v-if="baseInfo.type === ServiceProviderType.ZHONGMIAO">
        <div class="card-title">冻结记录</div>
        <div class="card-content">
          <h-table :columns="freezeRecordColumns" :data-source="freezeRecordData" :pagination="false" size="small"
            :bordered="true" />
        </div>
      </div> -->

      <!-- 违规记录 - 所有类型都显示 -->
      <div class="info-card">
        <div class="card-title">违规记录</div>
        <div class="card-content">
          <h-table :columns="violationColumns" :data-source="detailData.merchantExamineRecordQueryResults || []"
            :pagination="false" size="small" :bordered="true" />
        </div>
      </div>

      <!-- 产品信息 - 酒店显示 1-->
      <div class="info-card" v-if="detailData.merchantType === MerchantTypeEnum.HOTEL">
        <div class="card-title">产品信息</div>
        <div class="card-content">
          <h-table :columns="productColumns" :data-source="detailData.resourceHotelResults || []" :pagination="false"
            size="small" :bordered="true" />
        </div>
      </div>

      <!-- 转正记录 - 旅行社显示 -->
      <div class="info-card" v-if="detailData.merchantType === MerchantTypeEnum.TRAVEL">
        <div class="card-title">转正记录</div>
        <div class="card-content">
          <h-table :columns="regularizationColumns" :data-source="detailData.merchantTrialRecords || []"
            :pagination="false" size="small" :bordered="true" />
        </div>
      </div>
    </div>
  </div>

  <!-- 详情弹框 -->
  <Modal :open="detailModalVisible" :title="title" @cancel="detailModalVisible = false" :footer="null" width="500px">
    <div v-if="currentDetail" class="detail-content">
      <div class="detail-section">
        <div class="detail-item">保证金单号：{{ currentDetail.recordNo }}</div>
        <div class="detail-item">操作类别：{{ currentDetail.type === 1 ? '缴纳' : '退款' }}</div>
        <div class="detail-item">金额：{{ currentDetail.type === 1 ? '+' : '-' }}{{ currentDetail.amount }}元</div>
        <div class="detail-item">
          创建时间：{{
            currentDetail.gmtCreate ? dayjs(currentDetail.gmtCreate).format('YYYY-MM-DDHH: mm: ss') : ' - '
          }}
        </div>
        <div class="detail-item">收款单号：{{ currentDetail.sapReceiveNo }}</div>
        <div class="detail-item">确认收款人：{{ currentDetail.receiveName }}</div>
        <div class="detail-item">
          确认时间：{{
            currentDetail.receiveTime ? dayjs(currentDetail.receiveTime).format('YYYY-MM-DDHH: mm: ss') : ' - '
          }}
        </div>
        <div class="detail-item">
          <div v-if="currentDetail.attachmentFiles && currentDetail.attachmentFiles.length > 0">
            <div v-for="(item, index) in currentDetail.attachmentFiles" :key="index" class="file-item" style="">
              <span class="file-type">附件：</span>
              <a :href="item" target="_blank">{{ item.split('/').pop() }}</a>
            </div>
          </div>
          <span v-else>-</span>
        </div>
      </div>
    </div>
  </Modal>
  <!-- 通知弹框逻辑 -->
  <div v-if="noticeVisible">
    <notice @ok="handleShow" @cancel="handlecancel"></notice>
  </div>
</template>

<style scoped lang="less">
.service-provider-detail-container {
  height: 100%;
  width: 100%;
  background-color: #fff;
  padding: 24px;
  overflow: auto;

  .service-provider-detail-content {
    .info-card {
      margin-bottom: 24px;
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);

      .card-title {
        font-size: 16px;
        font-weight: 500;
        color: #333;
        font-weight: bold;
        padding: 16px 24px;
        border-bottom: 1px solid #f0f0f0;

        &.main-title {
          font-size: 24px;
          font-weight: 600;
        }

        &.deposit-header {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .deposit-balance-simple {
            .balance-label {
              color: #666;
              margin-right: 8px;
            }

            .balance-value {
              font-size: 18px;
              color: #1890ff;
              font-weight: bold;
            }
          }
        }
      }

      .info-content {
        padding: 24px;

        .company-name {
          font-size: 20px;
          font-weight: 500;
          color: #333;
          margin-bottom: 8px;
          display: inline-block;
          margin-right: 8px;
        }

        .company-type {
          background: #f5f5f5;
          color: #666;
          margin-bottom: 16px;
        }

        .info-item {
          margin-bottom: 16px;

          .label {
            color: black;
            margin-right: 8px;
            font-weight: bold;
          }

          .value {
            color: black;
            font-weight: bold;
          }
        }
      }

      .card-content {
        padding: 24px;

        .sub-title {
          font-size: 16px;
          font-weight: 500;
          color: #333;
          margin-bottom: 16px;
          padding-bottom: 8px;
          border-bottom: 1px solid #f0f0f0;
        }

        .deposit-balance {
          background: #f8f8f8;
          padding: 16px;
          border-radius: 4px;
          height: 100%;

          .balance-info {
            padding: 16px 0;

            .balance-item {
              margin-bottom: 16px;

              .label {
                color: #666;
                margin-right: 8px;
              }

              .value {
                color: #333;
                font-weight: 500;
                font-size: 16px;
              }
            }
          }
        }
      }

      :deep(.ant-table-wrapper) {
        .ant-table {
          border: 1px solid #f0f0f0;
        }
      }
    }
  }
}

.detail-content {
  padding: 0 24px;
}

.detail-item {
  margin-bottom: 16px;
  display: flex;

  &:last-child {
    margin-bottom: 0;
  }
}

.detail-item .label {
  color: #666;
  width: 80px;
  flex-shrink: 0;
}

.detail-item .value {
  color: #333;
  flex: 1;

  &.link {
    color: #1890ff;
    cursor: pointer;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
}

.detail-footer {
  margin-top: 24px;
  text-align: center;
  padding: 16px 0;
  border-top: 1px solid #f0f0f0;
}

/* 通用样式 */
.label {
  color: #666;
  margin-right: 8px;
}

.value {
  color: #333;
  font-weight: 500;
}

/* 文件项样式 */
.file-item {
  display: flex;
  align-items: center;
  margin-bottom: 18px;
}

.remark-text {
  display: inline-block;
  max-width: 350px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
}
</style>
