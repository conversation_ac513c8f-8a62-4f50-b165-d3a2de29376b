<script setup lang="ts">
import eHeader from '@haierbusiness-front/components/layout2/Header.vue';
import aiAssistant from '@haierbusiness-front/components/aiAssistant/AiAssistant.vue';
// import AIImg from '@/assets/image/home/<USER>';
import {throttle} from 'lodash';
import {onMounted, onUnmounted, ref, watch} from 'vue';
import {useRoute} from 'vue-router';
import {resolveParam} from '@haierbusiness-front/utils';
import {applicationStore} from "@haierbusiness-front/utils/src/store/applicaiton";
import {storeToRefs} from "pinia";
import {aiAssistantStore} from "@/store/aiAssistantStore";

const route = useRoute();

const store = aiAssistantStore()

const theme = ref<'primary' | 'default'>('default');
const onScroll = throttle(() => {
  if (window.scrollY > 78) {
    theme.value = 'primary';
  } else {
    theme.value = 'default';
  }
}, 200);

const hideBtn = ref<string>('');

onMounted(() => {
  const record = resolveParam(route.query.record);
  hideBtn.value = record.hideBtn || '';

  if (route.path === '/index') {
    theme.value = 'default';
    window.addEventListener('scroll', onScroll);
  } else {
    theme.value = 'primary';
  }
});

onUnmounted(() => {
  window.removeEventListener('scroll', onScroll);
});

watch(route, () => {
  if (route.path === '/index') {
    theme.value = 'default';
    window.addEventListener('scroll', onScroll);
  } else {
    theme.value = 'primary';
    window.removeEventListener('scroll', onScroll);
  }
});

// 新增接收数据的方法
const handleCloseAndPassData = (miceRequestData: any) => {
  // 使用Pinia存储数据
  store.miceRequestData = miceRequestData
};
</script>

<template>
  <div class="home">
    <div v-if="hideBtn !== '1'">
      <e-header :theme="theme"/>
      <div class="space"/>
    </div>
    <router-view/>
    <ai-assistant :top="230" @close-and-pass-data="handleCloseAndPassData">
    </ai-assistant>
  </div>
</template>

<style scoped lang="less">
.home {
  font-family: PingFangSC, PingFang SC;
  position: relative;
  height: auto;
  overflow: hidden;
  background: #f6f9fc;

  .space {
    height: 78px;
  }
}
</style>
