<script lang="ts" setup>
import { ref, onMounted, useAttrs, inject, watch, watchEffect, provide } from 'vue';
import { Modal, message } from 'ant-design-vue';
import Scheme from '@haierbusiness-front/components/mice/scheme/component/index.vue';
import { useRouter, useRoute } from 'vue-router';
import { getDealTime, routerParam, resolveParam } from '@haierbusiness-front/utils';
import finalNode from '@haierbusiness-front/components/mice/orderList/finalNode.vue';
const router = useRouter();
const route = useRoute();
const footerChecked = ref('b');
const isCloseLastTab = inject<Ref<boolean>>('isCloseLastTab');
const scheme = ref(null);

// 方案视图
const handleSchemeView = (type) => {
  let query = {
    ...route.query,
    type: type,
  };
  if (route.path === '/bidman/scheme/index') router.push({ path: '/bidman/scheme/view', query: query });
  else if (route.path === '/bidman/scheme/confirm') {
    if (resolveParam(route.query.record).orderType === 'detail') {
      if (type === 'a') router.push({ path: '/bidman/scheme/confirm/view', query: query });
      else if (type !== footerChecked.value && ['b', 'c'].includes(type)) {
        router.push({ path: '/bidman/scheme/confirm', query: query });
        setTimeout(() => {
          scheme.value?.getList();
          // isCloseLastTab.value = true;
        }, 1000);
      }
      // isCloseLastTab.value = true;
      return;
    }
    router.push({ path: '/bidman/scheme/confirm/view', query: query });
  } else if (route.path === '/bidman/bid/index') {
    router.push({ path: '/bidman/bid/view', query: query });
  }
  // isCloseLastTab.value = true;
};

const saveSchemeH = () => {
  if (route.path === '/bidman/scheme/confirm') {
    scheme.value.saveSchemeConfirm();
  } else if (route.path === '/bidman/scheme/index') {
    scheme.value.saveScheme();
  } else if (route.path === '/bidman/bid/index') {
    scheme.value.saveScheme();
    console.log('发布竞价');
  }
};
const showBtnT = ref(true);
const showBtn = () => {
  let text = '';
  if (route.path === '/bidman/scheme/index') {
    text = '提交';
  } else if (route.path === '/bidman/scheme/confirm') text = '方案确认';
  else if (route.path === '/bidman/bid/index') text = '发布竞价';
  return text;
};

const hideBtn = ref('0');
const frameModel = ref(inject<any>('frameModel'));
const $attrs = useAttrs();
const backupOrderList = () => {
  if ($attrs.orderSource === 'manage') {
    router.push('/bidman/orderList/index');
    // 关闭当前页签
    isCloseLastTab.value = true;
  } else {
    const localUrl = window.location.href;

    const businessMiceBid = import.meta.env.VITE_BUSINESS_INDEX_URL + '#';

    // 跳转需求确认页面
    const url =
      (localUrl.includes('/localhost') ? 'http://localhost:5183/#' : businessMiceBid) +
      '/card-order/miceOrder?record=' +
      routerParam(resolveParam(route.query.record));
    window.location.href = url;
    return;
  }
};
const schemeSumInitiate = ref([]);
provide('schemeSumInitiate', schemeSumInitiate);
watch(
  schemeSumInitiate,
  (newVal) => {
    console.log(newVal);
    if (newVal) {
      newVal.forEach((item) => {
        if (!item.isExclude) showBtnT.value = false;
      });
    }
    if (newVal.length === 0) showBtnT.value = true;
  },
  { deep: true },
);
onMounted(() => {
  footerChecked.value = route.query.type || 'b';
  const record = resolveParam(route.query.record);
  hideBtn.value = record?.hideBtn || '0';
  frameModel.value = hideBtn.value === '1' ? 1 : 0;
});
</script>

<template>
  <div>
    <Scheme v-bind="$attrs" ref="scheme">
      <template #processSlot>
        <!-- 流程信息 -->
        <div class="" v-show="showProcess">
          <finalNode :nodeId="route.query.record" :key="route.fullPath"></finalNode>
        </div>
      </template>
      <!-- 底部插槽：操作按钮组 -->
      <template #footer>
        <a-radio-group v-model:value="footerChecked" button-style="solid">
          <a-radio-button @click="handleSchemeView('a')" value="a">需求视图</a-radio-button>
          <a-radio-button @click="handleSchemeView('b')" value="b">方案视图</a-radio-button>
          <a-radio-button
            v-if="
              ['BID_RESULT_CONFIRM'].includes(scheme?.miceDetail?.processNode) &&
              resolveParam(route.query.record).orderType === 'detail'
            "
            @click="handleSchemeView('c')"
            value="c"
            >竞价方案</a-radio-button
          >
        </a-radio-group>
        <a-button
          v-if="
            (hideBtn !== '1' && ['/bidman/scheme/confirm'].includes(route.path) && $attrs.orderSource === 'manage') ||
            resolveParam(route.query.record).orderType === 'detail' ||
            (showBtnT && ['SCHEME_APPROVAL'].includes(scheme?.miceDetail?.processNode))
          "
          style="float: right"
          @click="backupOrderList()"
          type="primary"
          >返回</a-button
        >
        <a-button v-show="hideBtn !== '1'" v-else style="float: right" type="primary" @click="saveSchemeH">{{
          showBtn()
        }}</a-button>
      </template>
    </Scheme>
  </div>
</template>
